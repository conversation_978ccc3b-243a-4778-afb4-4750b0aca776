<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { Repos, type MomRole } from '../../../../xtrade-sdk/dist';
import { ElMessage } from 'element-plus';
import { getDataScopes } from '@/script';

const repoInstance = new Repos.AdminRepo();
const dataScopes = getDataScopes();

type FormRole = Pick<MomRole, 'roleName' | 'description' | 'userType'>;

const { role } = defineProps<{
  role?: MomRole;
}>();

const visible = defineModel<boolean>();
const formRef = useTemplateRef('formRef');

const emit = defineEmits<{
  success: [];
}>();

// 表单校验规则
const rules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  userType: [{ required: true, message: '请选择数据权限', trigger: 'blur' }],
};

const form = ref<FormRole>({
  roleName: '',
  userType: dataScopes[0].value,
  description: '',
});

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (role) {
      const matchedType = dataScopes.find(x => x.value == role.userType);
      form.value = {
        roleName: role.roleName,
        userType: matchedType?.value || (null as any),
        description: role.description,
      };
    }
  }
});

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (role) {
        const { errorCode, errorMsg } = await repoInstance.UpdateRole({
          ...role,
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success');
          ElMessage.success('修改成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      } else {
        const { errorCode, errorMsg } = await repoInstance.CreateRole({
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success');
          ElMessage.success('添加成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      }
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  form.value = {
    roleName: '',
    userType: dataScopes[0].value,
    description: '',
  };
  formRef.value?.resetFields();
};
</script>
<template>
  <el-dialog
    :model-value="visible"
    :title="role ? '修改角色' : '新建角色'"
    width="350px"
    @close="handleClose"
    draggable
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="90px">
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="form.roleName" placeholder="请输入角色名称" />
      </el-form-item>
      <el-form-item label="数据权限" prop="userType">
        <el-select v-model="form.userType" placeholder="请选择数据权限">
          <el-option
            v-for="item in dataScopes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="角色描述" prop="description">
        <el-input v-model="form.description" :rows="3" type="textarea" placeholder="角色描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>
