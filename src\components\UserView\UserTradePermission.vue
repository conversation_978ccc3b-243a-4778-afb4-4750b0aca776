<script setup lang="ts">
import { ref, shallowRef, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { MomUser } from '../../../../xtrade-sdk/dist';

const { user } = defineProps<{
  user?: MomUser;
}>();

// 交易权限配置
const tradePermissions = shallowRef({
  // 交易类型权限
  tradeTypes: [
    { key: 'buy', label: '买入', enabled: true },
    { key: 'sell', label: '卖出', enabled: true },
    { key: 'margin_buy', label: '融资买入', enabled: false },
    { key: 'margin_sell', label: '融券卖出', enabled: false },
    { key: 'repay', label: '还券', enabled: false },
  ],

  // 交易限制
  tradeLimits: {
    maxOrderAmount: 1000000, // 单笔最大金额
    maxDailyAmount: 5000000, // 日最大交易金额
    maxPositionRatio: 80, // 最大持仓比例
    allowNightTrading: false, // 允许夜盘交易
    allowWeekendTrading: false, // 允许周末交易
  },

  // 风控设置
  riskControl: {
    enableRiskControl: true, // 启用风控
    maxLossRatio: 10, // 最大亏损比例
    warningLossRatio: 5, // 预警亏损比例
    forceCloseRatio: 15, // 强制平仓比例
    enablePositionLimit: true, // 启用持仓限制
  },
});

// 表单验证规则
const validateAmount = (rule: any, value: any, callback: any) => {
  if (value && value <= 0) {
    callback(new Error('金额必须大于0'));
  } else {
    callback();
  }
};

const validateRatio = (rule: any, value: any, callback: any) => {
  if (value && (value < 0 || value > 100)) {
    callback(new Error('比例必须在0-100之间'));
  } else {
    callback();
  }
};

// 保存权限设置
const handleSave = () => {
  // 这里应该调用API保存交易权限设置
  console.log('保存交易权限:', tradePermissions.value);
  ElMessage.success('交易权限保存成功');
};

// 重置为默认设置
const handleReset = () => {
  ElMessageBox.confirm('确定要重置为默认设置吗？', '确认重置', {
    type: 'warning',
  }).then(() => {
    // 重置为默认值
    tradePermissions.value.tradeTypes.forEach(type => {
      type.enabled = ['buy', 'sell'].includes(type.key);
    });

    tradePermissions.value.tradeLimits = {
      maxOrderAmount: 1000000,
      maxDailyAmount: 5000000,
      maxPositionRatio: 80,
      allowNightTrading: false,
      allowWeekendTrading: false,
    };

    tradePermissions.value.riskControl = {
      enableRiskControl: true,
      maxLossRatio: 10,
      warningLossRatio: 5,
      forceCloseRatio: 15,
      enablePositionLimit: true,
    };

    ElMessage.success('已重置为默认设置');
  });
};
</script>

<template>
  <div p-6>
    <div mb-4 flex="~ items-center justify-between">
      <div>
        <h3 text-lg font-medium mb-2>交易权限管理</h3>
        <p text-sm text-gray-600>配置用户的交易权限和风控参数</p>
      </div>
      <div flex="~ items-center gap-3">
        <el-button @click="handleReset">重置默认</el-button>
        <el-button type="primary" @click="handleSave">保存设置</el-button>
      </div>
    </div>

    <div space-y-6>
      <!-- 交易类型权限 -->
      <el-card>
        <template #header>
          <div flex="~ items-center gap-2">
            <el-icon><TrendCharts /></el-icon>
            <span font-medium>交易类型权限</span>
          </div>
        </template>

        <div grid="~ cols-2 md:cols-3 lg:cols-5" gap-4>
          <div
            v-for="tradeType in tradePermissions.tradeTypes"
            :key="tradeType.key"
            p-4
            border
            rounded-lg
            :class="tradeType.enabled ? 'border-blue-200 bg-blue-50' : 'border-gray-200'"
            transition-colors
          >
            <el-checkbox v-model="tradeType.enabled" size="large">
              {{ tradeType.label }}
            </el-checkbox>
          </div>
        </div>
      </el-card>

      <!-- 交易限制 -->
      <el-card>
        <template #header>
          <div flex="~ items-center gap-2">
            <el-icon><Lock /></el-icon>
            <span font-medium>交易限制</span>
          </div>
        </template>

        <el-form label-width="140px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="单笔最大金额">
                <el-input-number
                  v-model="tradePermissions.tradeLimits.maxOrderAmount"
                  :min="0"
                  :step="10000"
                  w-full
                  controls-position="right"
                />
                <span ml-2 text-sm text-gray-500>元</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日最大交易金额">
                <el-input-number
                  v-model="tradePermissions.tradeLimits.maxDailyAmount"
                  :min="0"
                  :step="100000"
                  w-full
                  controls-position="right"
                />
                <span ml-2 text-sm text-gray-500>元</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最大持仓比例">
                <el-input-number
                  v-model="tradePermissions.tradeLimits.maxPositionRatio"
                  :min="0"
                  :max="100"
                  w-full
                  controls-position="right"
                />
                <span ml-2 text-sm text-gray-500>%</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交易时间">
                <div space-y-2>
                  <el-checkbox v-model="tradePermissions.tradeLimits.allowNightTrading">
                    允许夜盘交易
                  </el-checkbox>
                  <el-checkbox v-model="tradePermissions.tradeLimits.allowWeekendTrading">
                    允许周末交易
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 风控设置 -->
      <el-card>
        <template #header>
          <div flex="~ items-center gap-2">
            <el-icon><Shield /></el-icon>
            <span font-medium>风控设置</span>
          </div>
        </template>

        <el-form label-width="140px">
          <el-form-item label="启用风控">
            <el-switch v-model="tradePermissions.riskControl.enableRiskControl" />
          </el-form-item>

          <div v-if="tradePermissions.riskControl.enableRiskControl">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="预警亏损比例">
                  <el-input-number
                    v-model="tradePermissions.riskControl.warningLossRatio"
                    :min="0"
                    :max="100"
                    w-full
                    controls-position="right"
                  />
                  <span ml-2 text-sm text-gray-500>%</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大亏损比例">
                  <el-input-number
                    v-model="tradePermissions.riskControl.maxLossRatio"
                    :min="0"
                    :max="100"
                    w-full
                    controls-position="right"
                  />
                  <span ml-2 text-sm text-gray-500>%</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="强制平仓比例">
                  <el-input-number
                    v-model="tradePermissions.riskControl.forceCloseRatio"
                    :min="0"
                    :max="100"
                    w-full
                    controls-position="right"
                  />
                  <span ml-2 text-sm text-gray-500>%</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="持仓限制">
                  <el-switch v-model="tradePermissions.riskControl.enablePositionLimit" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<style scoped></style>
