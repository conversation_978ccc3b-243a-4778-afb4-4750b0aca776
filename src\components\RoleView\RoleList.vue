<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, shallowRef } from 'vue';
import { ElMessage, ElMessageBox, TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { putRow, renderLabel } from '@/script';
import { MomUserType, Repos, type MomRole } from '../../../../xtrade-sdk/dist';
import RoleDialog from './RoleDialog.vue';
import RoleMenuTree from './RoleMenuTree.vue';

interface CellRenderParam {
  rowData: MomRole;
  cellData: any;
}

const userTypes = Object.values(MomUserType);
const repoInstance = new Repos.AdminRepo();

// 基础列定义
const columns: ColumnDefinition<MomRole> = [
  { key: 'roleName', title: '角色名称', width: 150, sortable: true },
  { key: 'userType', title: '数据权限', width: 100, sortable: true, cellRenderer: renderUserType },
  { key: 'description', title: '角色描述', width: 200, sortable: true },
  {
    key: 'activeFlag',
    title: '是否启用',
    width: 100,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomRole }) => {
      return (
        <el-switch modelValue={rowData.activeFlag} before-change={() => beforeChange(rowData)} />
      );
    },
  },
];

// 行操作
const rowActions: RowAction<MomRole>[] = [
  {
    label: '修改',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function renderUserType(params: CellRenderParam) {
  return <span>{renderLabel(params.cellData as number, userTypes)}</span>;
}

const records = shallowRef<MomRole[]>([]);
const visible = shallowRef(false);
const role = shallowRef<MomRole | undefined>();
const selectedRole = shallowRef<MomRole | undefined>();

function handleAdd() {
  visible.value = true;
  role.value = undefined;
}

function editRow(row: MomRole) {
  visible.value = true;
  role.value = row;
}

function deleteRow(row: MomRole) {
  ElMessageBox.confirm(`确认删除角色：${row.roleName}？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const { errorCode, errorMsg } = await repoInstance.DeleteRole(row.id);
    if (errorCode === 0) {
      ElMessage.success('删除成功');
      request();
    } else {
      ElMessage.error(errorMsg || '删除失败');
    }
  });
}

async function request() {
  records.value = (await repoInstance.QueryRoles()).data || [];
}

function handleRowClick(row: MomRole) {
  selectedRole.value = row;
}

const beforeChange = async (rowData: MomRole) => {
  const { errorCode, errorMsg } = await repoInstance.UpdateRole({
    ...rowData,
    activeFlag: !rowData.activeFlag,
  });
  if (errorCode === 0) {
    putRow(
      {
        ...rowData,
        activeFlag: !rowData.activeFlag,
      },
      records,
    );
    return true;
  } else {
    ElMessage.error(errorMsg || '操作失败');
    return false;
  }
};

onMounted(() => {
  request();
});
</script>

<template>
  <div w-full flex gap-10>
    <div w="50%">
      <VirtualizedTable
        ref="tableRef"
        :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
        :columns="columns"
        :data="records"
        :row-actions="rowActions"
        :row-action-width="150"
        select
        fixed
        @row-click="handleRowClick"
      >
        <template #actions>
          <div class="actions" flex aic>
            <el-button type="primary" @click="handleAdd">
              <i class="iconfont icon-role" mr-5></i>
              <span>新建角色</span>
            </el-button>
          </div>
        </template>
      </VirtualizedTable>
      <RoleDialog v-model="visible" :role="role" @success="request" />
    </div>
    <RoleMenuTree flex-1 :role="selectedRole" />
  </div>
</template>

<style scoped></style>
