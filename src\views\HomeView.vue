<script setup lang="ts">
import HomeMenu from '@/components/HomeView/HomeMenu.vue';
import HomeTitle from '@/components/HomeView/HomeTitle.vue';
import HomeTabs from '@/components/HomeView/HomeTabs.vue';
import HomeToolkit from '@/components/HomeView/HomeToolkit.vue';
import HomeContent from '@/components/HomeView/HomeContent.vue';
import BottomBar from '@/components/HomeView/BottomBar.vue';
import { computed, onBeforeMount, reactive, ref, watch } from 'vue';
import { getLocal, getUser, setLocal } from '@/script';
import type { MomMenuTree } from '../../../xtrade-sdk/dist';
import { AdminService } from '@/api';

const activeMenu = ref<MomMenuTree | null>(null);
const activeMenus = ref<MomMenuTree[]>([]);
const fullMenus = ref<MomMenuTree[]>([]);

const activeMenuNames = computed(() => {
  return activeMenus.value.map(item => item.menuRoute!);
});

const states = reactive({
  showMenu: true,
});

watch(activeMenu, (newValue, oldValue) => {
  if (newValue?.menuRoute !== oldValue?.menuRoute) {
    setLocal('activeMenu', newValue!);
  }
});

watch(
  () => activeMenus.value.length,
  () => {
    setLocal('activeMenus', activeMenus.value);
  },
);

// 初始化tabs和当前菜单
onBeforeMount(async () => {
  const menus = await AdminService.getRoleMenuTree(getUser()!.roleId);
  fullMenus.value = menus;
  const localActiveMenu = getLocal<MomMenuTree>('activeMenu');
  if (localActiveMenu) {
    activeMenu.value = localActiveMenu;
  } else {
    // 找到第一个有路由的菜单
    const findFirstMenuHasMenuRoute = (menus: MomMenuTree[]) => {
      for (const menu of menus) {
        if (menu.menuRoute) {
          activeMenu.value = menu;
          return;
        }
        if (menu.children?.length) {
          findFirstMenuHasMenuRoute(menu.children);
        }
      }
    };
    findFirstMenuHasMenuRoute(menus);
  }
  const localActiveMenus = getLocal<MomMenuTree[]>('activeMenus');
  if (localActiveMenus) {
    activeMenus.value = localActiveMenus;
  }
  // 如果activeMenus中没有activeMenu，则添加
  if (
    activeMenu.value &&
    !activeMenus.value.some(item => item.menuRoute === activeMenu.value?.menuRoute)
  ) {
    activeMenus.value.push(activeMenu.value);
  }
});

const handleClickMenu = (menu: MomMenuTree) => {
  activeMenu.value = menu;
  if (!activeMenus.value.some(item => item.menuRoute === menu.menuRoute)) {
    activeMenus.value.push(menu);
  }
};

const handleCloseTab = (menu: MomMenuTree) => {
  const menuIndex = activeMenus.value.findIndex(item => item.menuRoute === menu.menuRoute);
  if (menu.menuRoute === activeMenu.value?.menuRoute) {
    activeMenu.value = activeMenus.value[menuIndex - 1] || activeMenus.value[menuIndex + 1];
  }
  activeMenus.value.splice(menuIndex, 1);
};

function toggleShowMenu() {
  states.showMenu = !states.showMenu;
}
</script>

<template>
  <div class="home-view-root" h-full flex flex-col>
    <div flex aic jcsb px-20 h-68 bg="[--g-block-bg-5]">
      <div class="home-title-box">
        <HomeTitle @toggle="toggleShowMenu" />
      </div>
      <div class="home-tabs-box" h-36 flex flex-1 min-w-1>
        <HomeTabs
          :active-menu="activeMenu"
          :tabs="activeMenus"
          @click-menu="handleClickMenu"
          @close-tab="handleCloseTab"
        />
      </div>
      <div class="home-toolkit-box">
        <HomeToolkit />
      </div>
    </div>
    <div flex flex-1 min-h-1 overflow-hidden>
      <HomeMenu
        v-show="states.showMenu"
        :active-menu="activeMenu"
        :menus="fullMenus"
        @click-menu="handleClickMenu"
      />
      <div class="home-content-box" ml-4 flex-1 min-w-1 flex flex-col>
        <HomeContent :menus="fullMenus" :include="activeMenuNames" :active-menu="activeMenu" />
      </div>
    </div>
    <BottomBar />
  </div>
</template>

<style scoped></style>
