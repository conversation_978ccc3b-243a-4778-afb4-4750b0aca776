<script setup lang="ts">
import { ref, shallowRef, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { MomUser } from '../../../../xtrade-sdk/dist';

const { user } = defineProps<{
  user?: MomUser;
}>();

// 产品权限数据
const productPermissions = shallowRef([
  {
    id: 1,
    productCode: 'ABC234',
    productName: '易方达海外ETF2号',
    netValue: 1.2536,
    totalValue: 22.04,
    hasPermission: true,
    canView: true,
    canTrade: false,
  },
  {
    id: 2,
    productCode: 'DEF567',
    productName: '华夏沪深300ETF',
    netValue: 1.1234,
    totalValue: 15.67,
    hasPermission: false,
    canView: false,
    canTrade: false,
  },
  {
    id: 3,
    productCode: 'GHI890',
    productName: '南方中证500ETF',
    netValue: 0.9876,
    totalValue: 8.45,
    hasPermission: true,
    canView: true,
    canTrade: true,
  },
]);

// 搜索关键词
const searchKeyword = ref('');

// 过滤后的产品列表
const filteredProducts = computed(() => {
  if (!searchKeyword.value) {
    return productPermissions.value;
  }
  return productPermissions.value.filter(
    product =>
      product.productCode.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      product.productName.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  );
});

// 全选状态
const isAllSelected = computed(() => {
  return filteredProducts.value.length > 0 && filteredProducts.value.every(p => p.hasPermission);
});

// 处理全选
const handleSelectAll = (checked: string | number | boolean) => {
  filteredProducts.value.forEach(product => {
    product.hasPermission = checked as boolean;
    if (!checked) {
      product.canView = false;
      product.canTrade = false;
    }
  });
};

// 处理单个产品权限变更
const handlePermissionChange = (product: any, type: 'permission' | 'view' | 'trade') => {
  if (type === 'permission' && !product.hasPermission) {
    // 取消权限时，同时取消查看和交易权限
    product.canView = false;
    product.canTrade = false;
  } else if (type === 'view' && product.canView && !product.hasPermission) {
    // 开启查看权限时，自动开启基础权限
    product.hasPermission = true;
  } else if (type === 'trade' && product.canTrade) {
    // 开启交易权限时，自动开启基础权限和查看权限
    product.hasPermission = true;
    product.canView = true;
  }
};

// 保存权限设置
const handleSave = () => {
  // 这里应该调用API保存权限设置
  console.log('保存产品权限:', productPermissions.value);
  ElMessage.success('产品权限保存成功');
};
</script>

<template>
  <div p-6>
    <div mb-4 flex="~ items-center justify-between">
      <div>
        <h3 text-lg font-medium mb-2>产品权限管理</h3>
        <p text-sm text-gray-600>为用户配置可访问的产品权限</p>
      </div>
      <div flex="~ items-center gap-4">
        <el-input v-model="searchKeyword" placeholder="搜索产品代码或名称" w-60 clearable>
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleSave">保存设置</el-button>
      </div>
    </div>

    <div bg-white rounded-lg border>
      <div p-4 border-b flex="~ items-center gap-4">
        <el-checkbox :model-value="isAllSelected" @change="handleSelectAll">全选</el-checkbox>
        <span text-sm text-gray-600>
          已选择 {{ filteredProducts.filter(p => p.hasPermission).length }} /
          {{ filteredProducts.length }} 个产品
        </span>
      </div>

      <div max-h-96 overflow-y-auto>
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          p-4
          border-b
          last:border-b-0
          hover:bg-gray-50
          transition-colors
        >
          <div flex="~ items-center justify-between">
            <div flex="~ items-center gap-4" flex-1>
              <el-checkbox
                v-model="product.hasPermission"
                @change="() => handlePermissionChange(product, 'permission')"
              />

              <div flex-1>
                <div flex="~ items-center gap-3" mb-1>
                  <span font-medium>{{ product.productCode }}</span>
                  <span text-gray-600>{{ product.productName }}</span>
                </div>
                <div flex="~ items-center gap-6" text-sm text-gray-500>
                  <span>净值: {{ product.netValue }}</span>
                  <span>总值: {{ product.totalValue }}万</span>
                </div>
              </div>
            </div>

            <div flex="~ items-center gap-6">
              <el-checkbox
                v-model="product.canView"
                :disabled="!product.hasPermission"
                @change="() => handlePermissionChange(product, 'view')"
              >
                查看权限
              </el-checkbox>

              <el-checkbox
                v-model="product.canTrade"
                :disabled="!product.hasPermission"
                @change="() => handlePermissionChange(product, 'trade')"
              >
                交易权限
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <div v-if="filteredProducts.length === 0" p-8 text-center text-gray-500>
        <el-icon size="48" mb-2><DocumentRemove /></el-icon>
        <p>没有找到匹配的产品</p>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
