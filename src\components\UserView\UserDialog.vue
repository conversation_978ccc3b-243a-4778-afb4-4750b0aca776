<script setup lang="ts">
import { ref, computed, useTemplateRef } from 'vue';
import type { MomUser } from '../../../../xtrade-sdk/dist';
import UserBasicInfo from './UserBasicInfo.vue';
import UserProductPermission from './UserProductPermission.vue';
import UserTradePermission from './UserTradePermission.vue';
import UserMachineBinding from './UserMachineBinding.vue';
import UserOperationLog from './UserOperationLog.vue';
import UserSystemLog from './UserSystemLog.vue';
import { AdminService } from '@/api';
import { ElMessage } from 'element-plus';

const { user } = defineProps<{
  user?: MomUser;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

// 当前激活的标签页
const activeTab = ref('basic');

const basicInfoRef = useTemplateRef('basicInfoRef');
const machineBindingRef = useTemplateRef('machineBindingRef');

// 是否编辑模式
const isEdit = computed(() => !!user);

// 对话框标题
const dialogTitle = computed(() => (isEdit.value ? '编辑用户' : '新建用户'));

// 标签页配置
const basicTabs = [
  { name: 'basic', label: '基本信息', icon: 'court' },

  { name: 'machine', label: '机器绑定', icon: 'unlink' },
];

const logTabs = [
  { name: 'product', label: '产品权限', icon: 'password' },
  { name: 'trade', label: '交易权限', icon: 'puzzle' },
  { name: 'operation', label: '操作日志', icon: 'text' },
  { name: 'system', label: '系统日志', icon: 'tag' },
];

// 根据是否编辑模式显示不同的标签页
const tabs = computed(() => (isEdit.value ? [...basicTabs, ...logTabs] : basicTabs));

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  activeTab.value = 'basic';
};

const handleSave = async () => {
  const valid = await basicInfoRef.value?.validate();

  if (valid) {
    // 验证机器绑定
    const machineValid = await machineBindingRef.value?.validate();
    if (!machineValid) {
      ElMessage.warning('请检查机器绑定地址是否正确');
      return;
    }

    if (isEdit.value && user) {
      // 编辑用户
      const {
        // password,
        ...updateData
      } = {
        ...user,
        ...basicInfoRef.value?.form,
        ...machineBindingRef.value?.form,
      };
      const { errorCode, errorMsg } = await AdminService.updateUser(updateData);
      if (errorCode === 0) {
        emit('success');
        ElMessage.success('修改成功');
        handleClose();
      } else {
        ElMessage.error(errorMsg || '操作失败');
      }
    } else {
      // 创建用户
      const { errorCode, errorMsg } = await AdminService.createUser({
        ...basicInfoRef.value?.form,
        ...machineBindingRef.value?.form,
        password: '123456',
        // password: aesEncrypt(form.value.password!),
      });
      if (errorCode === 0) {
        emit('success');
        ElMessage.success('添加成功');
        handleClose();
      } else {
        ElMessage.error(errorMsg || '操作失败');
      }
    }
  } else {
    if (activeTab.value !== 'basic') {
      ElMessage.warning('请先完成基本信息的填写');
    }
  }
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="920px"
    @close="handleClose"
    draggable
    destroy-on-close
  >
    <el-tabs class="typical-tabs" v-model="activeTab">
      <el-tab-pane v-for="tab in tabs" :key="tab.name" :name="tab.name" :label="tab.label">
        <template #label>
          <div flex="~ items-center gap-2">
            <i class="iconfont" :class="`icon-${tab.icon}`"></i>
            <span>{{ tab.label }}</span>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <UserBasicInfo ref="basicInfoRef" v-show="activeTab === 'basic'" :user="user" />

    <UserProductPermission v-show="activeTab === 'product'" :user="user" />

    <UserTradePermission v-show="activeTab === 'trade'" :user="user" />

    <UserMachineBinding ref="machineBindingRef" v-show="activeTab === 'machine'" :user="user" />

    <UserOperationLog v-show="activeTab === 'operation'" :user="user" />

    <UserSystemLog v-show="activeTab === 'system'" :user="user" />

    <template #footer>
      <div class="dialog-footer">
        <el-button w-200 @click="handleClose">取消</el-button>
        <el-button w-200 type="primary" @click="handleSave">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
